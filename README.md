# 多站点财经监控器使用说明

## 功能特性

✅ **多站点监控**: 支持同时监控多个财经网站  
✅ **实时抓取**: 自动检测网页变化，实时获取最新财经新闻  
✅ **智能过滤**: 支持内容过滤、去重、黑白名单等功能  
✅ **多线程架构**: 每个网站独立线程，真正并行监控  
✅ **容错机制**: 自动重连、错误恢复  
✅ **灵活配置**: 丰富的配置选项和命令行参数  
✅ **JSON持久化**: 自动将新闻保存到JSON文件，支持数据持久化

## 支持的网站

目前监控器支持以下财经网站：

1. **金十数据** - https://www.jin10.com/
2. **新浪财经7x24** - https://finance.sina.com.cn/7x24/?tag=0

## 基本用法

### 监控所有网站
```bash
# 默认监控所有启用的网站
python jin10_web_monitor.py

# 明确指定监控所有2个网站
python jin10_web_monitor.py --sites 金十数据 新浪财经7x24
```

### 监控指定网站
```bash
# 只监控新浪财经
python jin10_web_monitor.py --sites 新浪财经7x24

# 监控所有两个网站
python jin10_web_monitor.py --sites 金十数据 新浪财经7x24
```

### 其他选项
```bash
# 显示浏览器窗口（调试用）
python jin10_web_monitor.py --no-headless

# 保存日志到文件
python jin10_web_monitor.py --save-log

# 禁用过滤器
python jin10_web_monitor.py --disable-filter

# 启用白名单模式
python jin10_web_monitor.py --enable-whitelist

# 指定JSON存储文件路径
python jin10_web_monitor.py --json-file my_news.json
```

## 配置说明

可以通过环境变量配置各网站的检查间隔：

- `JIN10_CHECK_INTERVAL` - 金十数据检查间隔（默认2秒）
- `SINA_CHECK_INTERVAL` - 新浪财经检查间隔（默认3秒）

## 输出格式

监控器会实时输出新闻，格式如下：
```
[时间] [网站名称] 新闻内容
```

例如：
```
[14:30:25] [金十数据] 央行今日进行1000亿元逆回购操作
[14:30:26] [新浪财经7x24] 人民币对美元中间价报7.1234
```

## JSON数据持久化

监控器会自动将所有新闻保存到JSON文件中，默认文件名为 `financial_news.json`。

### JSON数据格式

每条新闻记录包含以下字段：
```json
{
  "id": "网站名_时间戳_内容哈希",
  "timestamp": "2024-06-26 14:30:25",
  "source": "金十数据",
  "content": "央行今日进行1000亿元逆回购操作",
  "saved_at": "2024-06-26T14:30:25.123456"
}
```

### 查看存储的新闻

使用 `view_news.py` 工具查看和分析存储的新闻：

```bash
# 查看最新10条新闻
python view_news.py

# 查看最新20条新闻
python view_news.py --count 20

# 查看所有新闻
python view_news.py --count 0

# 按来源过滤新闻
python view_news.py --source 金十数据

# 按关键词搜索新闻
python view_news.py --keyword 央行 利率

# 显示统计信息
python view_news.py --stats

# 按时间倒序显示（最新在前）
python view_news.py --reverse

# 指定JSON文件
python view_news.py --file my_news.json

# 组合使用
python view_news.py --source 新浪财经7x24 --keyword 股市 --count 5 --stats
```

### JSON文件管理

- 文件会自动创建，无需手动创建
- 默认最多保存10,000条新闻记录，超出后自动删除旧记录
- 支持多线程安全写入
- 文件大小通常每1000条新闻约为几百KB

## 停止监控

按 `Ctrl+C` 停止所有监控器。

## 注意事项

1. 首次运行会自动下载 Chrome 浏览器驱动
2. 需要稳定的网络连接
3. 建议在服务器环境下使用 `--headless` 模式（默认已启用）
4. 监控器会自动处理网络断线和页面刷新 

# 财经新闻AI处理器使用说明

## 📋 概述

这是一个完整的单文件AI模块 (`financial_news_ai_processor.py`)，用于监控财经新闻并通过Google Gemini API进行智能处理。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests watchdog
```

### 2. 运行程序
```bash
# 自动监控模式 (推荐)
python financial_news_ai_processor.py

# 手动处理模式
python financial_news_ai_processor.py --manual

# 测试模式
python financial_news_ai_processor.py --test
```

**注意**: API密钥已内置在代码中，无需额外配置！

## 📖 使用方法

### 自动监控模式（常驻运行）
```bash
# 方式1: 直接运行
python financial_news_ai_processor.py

# 方式2: 使用启动脚本（推荐）
双击 "启动AI模块.bat"
```

**常驻运行特性:**
- 🚀 启动后自动进入常驻监控模式
- 📊 每60秒输出运行状态
- 🔔 实时检测 `financial_news.json` 文件变化
- ⚡ 检测到新内容时立即自动处理
- 💾 处理完成后立即保存结果
- 🔄 持续运行，直到手动停止

**停止方式:**
- 按 `Ctrl+C` 安全停止
- 或双击 "停止AI模块.bat"

### 手动处理模式
```bash
# 处理所有新消息
python financial_news_ai_processor.py --manual

# 限制处理数量
python financial_news_ai_processor.py --manual --count 5
```

### 测试模式
```bash
python financial_news_ai_processor.py --test
```
- 验证API密钥是否正确
- 测试处理一条消息
- 检查系统配置

### 创建测试数据
```bash
python financial_news_ai_processor.py --create-test
```

## 📁 文件结构

### 输入文件: `financial_news.json`
```json
[
  {
    "id": "金十数据_2025-06-26 04:48:32_89909",
    "timestamp": "2025-06-26 04:48:32",
    "source": "金十数据",
    "content": ":10 美国航空一客机发动机起火返航 无人受伤...",
    "saved_at": "2025-06-26T04:48:32.398431"
  }
]
```

### 输出文件: `financial_news_ai.json`
```json
[
  {
    "timestamp": "2025-06-26T08:30:15.123456",
    "content": "美国航空一架载有153名乘客和6名机组人员的客机因发动机起火冒烟紧急返回拉斯维加斯机场，飞机安全着陆无人受伤。#美国航空 #发动机起火 #拉斯维加斯 #航空安全"
  }
]
```

### 跟踪文件: `processed_news_ids.json`
```json
[
  "金十数据_2025-06-26 04:48:32_89909",
  "金十数据_2025-06-26 04:48:32_72410"
]
```

## ⚙️ 工作原理

1. **文件监控**: 监控 `financial_news.json` 文件变化
2. **新消息检测**: 识别未处理的新消息
3. **AI处理**: 使用Gemini API和中文提示词处理内容
4. **结果保存**: 将AI生成的摘要和关键词保存到 `financial_news_ai.json`
5. **重复避免**: 记录已处理的消息ID，避免重复处理

## 🎯 AI处理效果

### 输入示例
```
:21 美国总统特朗普重申，伊朗的所有三个核设施都已被完全摧毁。
```

### 输出示例
```
美国总统特朗普重申，伊朗境内的三座核设施已被彻底摧毁，进一步加剧了中东地区的紧张局势。#特朗普 #伊朗 #核设施 #中东
```

## 🔧 故障排除

### 常见问题

1. **"没有新消息需要处理"**
   - 检查 `financial_news.json` 是否存在且包含数据
   - 查看 `processed_news_ids.json` 确认消息未被处理过

2. **依赖库错误**
   ```bash
   pip install requests watchdog
   ```

3. **API调用失败**
   - 检查网络连接
   - 确认能访问Google API服务
   - 查看日志文件 `financial_news_ai.log`

4. **网络连接问题**
   - 确保能访问 `generativelanguage.googleapis.com`
   - 检查防火墙设置

### 日志文件
程序运行时会生成 `financial_news_ai.log` 日志文件，包含详细的运行信息和错误记录。

## 📊 性能特点

- **内存使用**: 最小化，逐条处理消息
- **API调用**: 限速1秒/次，避免超限
- **文件监控**: 低开销，2秒冷却时间
- **错误处理**: 健壮的异常处理机制

## 🔄 集成说明

此模块设计为与现有财经新闻监控系统协同工作：
- 读取相同的 `financial_news.json` 文件
- 不影响原有数据结构
- 独立运行，可随时启停

## 📝 注意事项

1. 确保有稳定的网络连接访问Gemini API
2. API调用会产生费用，请注意使用量
3. 建议在测试环境先验证功能
4. 定期检查日志文件了解运行状态

## 🆘 技术支持

如遇问题，请检查：
1. 日志文件 `financial_news_ai.log`
2. 环境变量设置
3. 网络连接状态
4. API密钥有效性

# 财经新闻AI处理器使用说明

## 📋 概述

这是一个完整的单文件AI模块 (`financial_news_ai_processor.py`)，用于监控财经新闻并通过Google Gemini API进行智能处理。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests watchdog
```

### 2. 运行程序
```bash
# 自动监控模式 (推荐)
python financial_news_ai_processor.py

# 手动处理模式
python financial_news_ai_processor.py --manual

# 测试模式
python financial_news_ai_processor.py --test
```

**注意**: API密钥已内置在代码中，无需额外配置！

## 📖 使用方法

### 自动监控模式（常驻运行）
```bash
# 方式1: 直接运行
python financial_news_ai_processor.py

# 方式2: 使用启动脚本（推荐）
双击 "启动AI模块.bat"
```

**常驻运行特性:**
- 🚀 启动后自动进入常驻监控模式
- 📊 每60秒输出运行状态
- 🔔 实时检测 `financial_news.json` 文件变化
- ⚡ 检测到新内容时立即自动处理
- 💾 处理完成后立即保存结果
- 🔄 持续运行，直到手动停止

**停止方式:**
- 按 `Ctrl+C` 安全停止
- 或双击 "停止AI模块.bat"

### 手动处理模式
```bash
# 处理所有新消息
python financial_news_ai_processor.py --manual

# 限制处理数量
python financial_news_ai_processor.py --manual --count 5
```

### 测试模式
```bash
python financial_news_ai_processor.py --test
```
- 验证API密钥是否正确
- 测试处理一条消息
- 检查系统配置

### 创建测试数据
```bash
python financial_news_ai_processor.py --create-test
```

## 📁 文件结构

### 输入文件: `financial_news.json`
```json
[
  {
    "id": "金十数据_2025-06-26 04:48:32_89909",
    "timestamp": "2025-06-26 04:48:32",
    "source": "金十数据",
    "content": ":10 美国航空一客机发动机起火返航 无人受伤...",
    "saved_at": "2025-06-26T04:48:32.398431"
  }
]
```

### 输出文件: `financial_news_ai.json`
```json
[
  {
    "timestamp": "2025-06-26T08:30:15.123456",
    "content": "美国航空一架载有153名乘客和6名机组人员的客机因发动机起火冒烟紧急返回拉斯维加斯机场，飞机安全着陆无人受伤。#美国航空 #发动机起火 #拉斯维加斯 #航空安全"
  }
]
```

### 跟踪文件: `processed_news_ids.json`
```json
[
  "金十数据_2025-06-26 04:48:32_89909",
  "金十数据_2025-06-26 04:48:32_72410"
]
```

## ⚙️ 工作原理

1. **文件监控**: 监控 `financial_news.json` 文件变化
2. **新消息检测**: 识别未处理的新消息
3. **AI处理**: 使用Gemini API和中文提示词处理内容
4. **结果保存**: 将AI生成的摘要和关键词保存到 `financial_news_ai.json`
5. **重复避免**: 记录已处理的消息ID，避免重复处理

## 🎯 AI处理效果

### 输入示例
```
:21 美国总统特朗普重申，伊朗的所有三个核设施都已被完全摧毁。
```

### 输出示例
```
美国总统特朗普重申，伊朗境内的三座核设施已被彻底摧毁，进一步加剧了中东地区的紧张局势。#特朗普 #伊朗 #核设施 #中东
```

## 🔧 故障排除

### 常见问题

1. **"没有新消息需要处理"**
   - 检查 `financial_news.json` 是否存在且包含数据
   - 查看 `processed_news_ids.json` 确认消息未被处理过

2. **依赖库错误**
   ```bash
   pip install requests watchdog
   ```

3. **API调用失败**
   - 检查网络连接
   - 确认能访问Google API服务
   - 查看日志文件 `financial_news_ai.log`

4. **网络连接问题**
   - 确保能访问 `generativelanguage.googleapis.com`
   - 检查防火墙设置

### 日志文件
程序运行时会生成 `financial_news_ai.log` 日志文件，包含详细的运行信息和错误记录。

## 📊 性能特点

- **内存使用**: 最小化，逐条处理消息
- **API调用**: 限速1秒/次，避免超限
- **文件监控**: 低开销，2秒冷却时间
- **错误处理**: 健壮的异常处理机制

## 🔄 集成说明

此模块设计为与现有财经新闻监控系统协同工作：
- 读取相同的 `financial_news.json` 文件
- 不影响原有数据结构
- 独立运行，可随时启停

## 📝 注意事项

1. 确保有稳定的网络连接访问Gemini API
2. API调用会产生费用，请注意使用量
3. 建议在测试环境先验证功能
4. 定期检查日志文件了解运行状态

## 🆘 技术支持

如遇问题，请检查：
1. 日志文件 `financial_news_ai.log`
2. 环境变量设置
3. 网络连接状态
4. API密钥有效性

# Telegram财经新闻机器人使用说明

## 📋 概述

这是一个独立的Telegram机器人模块，用于监听`financial_news_ai.json`文件的变化，筛选包含指定标签的财经新闻，并自动发送到指定的Telegram频道。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install python-telegram-bot watchdog
```

### 2. 创建Telegram Bot
1. 在Telegram中找到 [@BotFather](https://t.me/BotFather)
2. 发送 `/newbot` 创建新机器人
3. 按提示设置机器人名称和用户名
4. 获取Bot Token (格式: `123456789:ABCdefGhIJKlmNoPQRsTuVwXyZ`)

### 3. 获取频道ID
```bash
# 方法1: 公开频道
@your_channel_name

# 方法2: 私有频道
-1001234567890  # 数字ID
```

### 4. 配置机器人
编辑 `telegram_config.py` 文件：
```python
# 您的Bot Token
BOT_TOKEN = "123456789:ABCdefGhIJKlmNoPQRsTuVwXyZ"

# 目标频道ID
CHANNEL_ID = "@your_channel"  # 或 "-1001234567890"

# 目标标签 (根据需要修改)
TARGET_TAGS = [
    "特朗普", "美联储", "美国", "中国", "利率", "股市"
]
```

### 5. 运行机器人
```bash
# 方式1: 命令行
python telegram_news_bot.py

# 方式2: 双击启动脚本
启动Telegram机器人.bat
```

## ⚙️ 配置说明

### 基本配置
- `BOT_TOKEN`: 您的Telegram Bot Token
- `CHANNEL_ID`: 目标频道ID
- `TARGET_TAGS`: 目标标签列表

### 标签配置
机器人只会发送包含指定标签的消息。标签格式为 `#标签名`。

**预设标签分类:**
- **政治人物**: 特朗普、拜登、习近平等
- **央行机构**: 美联储、欧洲央行等
- **国家地区**: 美国、中国、欧洲等
- **经济指标**: 利率、通胀、GDP等
- **金融市场**: 股市、债券、外汇等
- **大宗商品**: 黄金、原油、天然气等
- **加密货币**: 比特币、以太坊等

### 高级配置
- `SEND_DELAY`: 消息发送间隔（避免频率限制）
- `MAX_MESSAGE_LENGTH`: 消息最大长度
- `RETRY_ATTEMPTS`: 发送失败重试次数
- `MESSAGE_TEMPLATE`: 消息格式模板

## 🎯 工作原理

1. **文件监控**: 实时监控 `financial_news_ai.json` 文件变化
2. **标签筛选**: 检查新消息是否包含目标标签
3. **消息格式化**: 使用模板格式化消息内容
4. **自动发送**: 发送到指定Telegram频道
5. **重复检测**: 避免发送重复消息

## 📊 运行状态

### 启动日志示例
```
🤖 启动Telegram财经新闻机器人...
✅ 机器人初始化成功，目标频道: @your_channel
🤖 机器人信息: @your_bot_name
🚀 机器人已启动，正在监控AI新闻文件...
🏷️ 目标标签: 特朗普, 美联储, 美国, 中国, 利率, 股市
```

### 处理消息示例
```
🔔 检测到AI新闻文件更新！
📥 发现 3 条新的AI记录
🎯 发现包含目标标签的消息: ['特朗普', '美联储']
✅ 消息发送成功到频道 @your_channel
📊 处理完成: 共 3 条记录，成功发送 1 条消息
```

## 📝 消息格式

### 默认格式
```
📈 **财经快讯**

美联储宣布维持利率不变，符合市场预期，但暗示未来可能根据经济数据调整政策。#美联储 #利率 #货币政策

🏷️ **相关标签**: 美联储, 利率
⏰ **时间**: 2025-07-20 19:30:15
```

### 自定义格式
可在 `telegram_config.py` 中修改 `MESSAGE_TEMPLATE`:
```python
MESSAGE_TEMPLATE = """🚨 **重要财经消息**

{content}

📌 **关键词**: {tags}
🕐 **发布时间**: {time}
📺 **来源**: AI财经分析"""
```

## 🔧 故障排除

### 常见问题

1. **"机器人启动失败"**
   - 检查Bot Token是否正确
   - 确认网络连接正常
   - 验证依赖库已安装

2. **"消息发送失败"**
   - 检查频道ID是否正确
   - 确认机器人已添加到频道
   - 验证机器人有发送消息权限

3. **"未检测到文件变化"**
   - 确认 `financial_news_ai.json` 文件存在
   - 检查文件路径是否正确
   - 验证AI模块是否正常运行

4. **"没有消息被发送"**
   - 检查目标标签配置
   - 确认AI消息包含指定标签
   - 查看日志了解筛选情况

### 调试模式
在 `telegram_config.py` 中启用调试:
```python
DEBUG_MODE = True
LOG_LEVEL = 'DEBUG'
```

### 日志文件
查看 `telegram_bot.log` 文件获取详细运行信息。

## 📁 文件结构

```
├── telegram_news_bot.py          # 主程序
├── telegram_config.py            # 配置文件
├── 启动Telegram机器人.bat        # 启动脚本
├── financial_news_ai.json        # AI处理结果(输入)
├── sent_telegram_messages.json   # 已发送消息记录
├── telegram_bot.log              # 运行日志
└── Telegram机器人使用说明.md     # 本说明文件
```

## 🔒 安全注意事项

1. **保护Bot Token**: 不要将Token提交到公开代码库
2. **频道权限**: 确保机器人只有必要的权限
3. **消息内容**: 注意财经信息的准确性和时效性
4. **频率限制**: 遵守Telegram API的频率限制

## 🚀 高级功能

### 多频道支持
可修改代码支持发送到多个频道:
```python
CHANNEL_IDS = ["@channel1", "@channel2", "-1001234567890"]
```

### 条件过滤
可添加更复杂的过滤条件:
```python
def custom_filter(content, tags):
    # 自定义过滤逻辑
    return True
```

### 消息统计
机器人会自动记录发送统计，可通过日志查看。

## 📞 技术支持

如遇问题，请检查：
1. 日志文件 `telegram_bot.log`
2. 配置文件 `telegram_config.py`
3. 网络连接和API访问
4. 机器人权限设置

# Telegram财经新闻机器人使用说明

## 📋 概述

这是一个独立的Telegram机器人模块，用于监听`financial_news_ai.json`文件的变化，根据严格的过滤条件筛选重要财经新闻，并自动发送到指定的Telegram频道。

## 🎯 **过滤规则 (重要更新)**

机器人只在满足以下条件之一时才会发送消息：

### 发送条件
1. **重要加密货币新闻**: 同时包含 `#重要` 和 `#加密货币相关` 标签
2. **特朗普相关新闻**: 包含 `#特朗普` 标签  
3. **马斯克相关新闻**: 包含 `#马斯克` 标签

### 发送条件示例
```
✅ 发送: #重要 #加密货币相关 #比特币
✅ 发送: #特朗普 #美国 #政策
✅ 发送: #马斯克 #特斯拉 #股价
❌ 不发送: #重要 #美联储 #利率 (只有重要，没有加密货币相关)
❌ 不发送: #加密货币相关 #以太坊 (只有加密货币相关，没有重要)
❌ 不发送: #以色列 #军事 (不满足任何发送条件)
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install python-telegram-bot==20.7 watchdog
```

### 2. 配置信息
机器人配置已内置在 `telegram_config.py` 中：
- **Bot Token**: 已配置
- **频道ID**: 已配置  
- **目标标签**: 重要, 加密货币相关, 特朗普, 马斯克, 以色列

### 3. 启动机器人
```bash
python telegram_news_bot.py
```

## ⚙️ 工作原理

### 1. 启动时历史基线设置
```
📋 启动时设置历史基线: 共 156 条历史消息
📅 启动时间: 2025-07-20 23:18:30
🔄 只会发送启动后新增的消息
```

### 2. 实时文件监控
```
🔔 检测到AI新闻文件更新！
📁 监听文件: C:\Users\<USER>\Desktop\jin10\financial_news_ai.json
```

### 3. 内容分析和过滤
```
📝 监听到第 1 条内容:
⏰ 时间戳: 2025-07-20T23:02:42.280358
📄 内容: 在比特币大幅上涨后，以太坊等加密货币也出现集体上涨...
🎯 发现满足发送条件的消息，包含标签: ['加密货币相关']
🏷️ 消息不满足发送条件，跳过 (只有加密货币相关，缺少重要标签)
```

### 4. 消息发送
```
✅ 消息发送成功到频道 -1002558446307
📊 处理完成: 共 1 条记录，成功发送 1 条消息
```

## 📱 消息格式

发送到Telegram的消息格式：
```
美国商务部长卢特尼克认可了鲍威尔关于维持高利率每年将使美国经济付出约7000亿美元代价的评估。#重要 #高利率 #经济成本 #7000亿美元 #卢特尼克 #鲍威尔 #美国经济

——————————
2025-07-20 22:56:40
```

## 🔧 配置文件说明

### telegram_config.py 主要配置
```python
# Bot配置 (已设置)
BOT_TOKEN = "8124246725:AAE1Uq5S1BLfwWXSF0TllGhtPNA6I_GKXss"
CHANNEL_ID = "-1002558446307"

# 目标标签
TARGET_TAGS = [
    "重要", "加密货币相关", "特朗普", "马斯克", "以色列"
]

# 发送配置
SEND_DELAY = 2  # 发送间隔
RETRY_ATTEMPTS = 3  # 重试次数
MAX_MESSAGE_LENGTH = 4000  # 最大长度

# 消息模板
MESSAGE_TEMPLATE = """{content}

——————————
{time}"""
```

## 📊 运行状态监控

### 启动日志示例
```
🤖 启动Telegram财经新闻机器人...
✅ 机器人初始化成功，目标频道: -1002558446307
🤖 机器人信息: @your_bot_username
📋 设置启动历史基线...
📋 启动时设置历史基线: 共 156 条历史消息
📅 启动时间: 2025-07-20 23:18:30
🔄 只会发送启动后新增的消息
🚀 机器人已启动，正在监控AI新闻文件...
📁 监听文件: C:\Users\<USER>\Desktop\jin10\financial_news_ai.json
🏷️ 目标标签: 重要, 加密货币相关, 特朗普, 马斯克, 以色列
📋 发送条件:
   1. 同时包含 '重要' 和 '加密货币相关' 标签
   2. 或者包含 '特朗普' 标签
   3. 或者包含 '马斯克' 标签
🛑 按 Ctrl+C 停止
💓 机器人运行正常，已发送消息: 28 条
```

### 处理消息日志示例
```
🔔 检测到AI新闻文件更新！
📁 监听文件: C:\Users\<USER>\Desktop\jin10\financial_news_ai.json
📥 发现 1 条新的AI记录
📝 监听到第 1 条内容:
⏰ 时间戳: 2025-07-20T23:25:15.123456
📄 内容: 特朗普宣布新的贸易政策，将对中国商品征收额外关税...
🎯 发现满足发送条件的消息，包含标签: ['特朗普']
✅ 第 1 条消息已发送到频道
📊 处理完成: 共 1 条记录，成功发送 1 条消息
```

## 🛠️ 故障排除

### 常见问题

1. **发送超时问题**
```
⏰ 发送超时，重试 1/3，等待 10 秒...
⏰ 发送超时，重试 2/3，等待 15 秒...
⏰ 发送超时，重试 3/3，等待 20 秒...
```
**解决方案**: 检查网络连接，机器人会自动重试

2. **消息不发送**
```
🏷️ 消息不满足发送条件，跳过
```
**原因**: 消息不满足严格的过滤条件

3. **重复消息检测**
```
📝 消息已发送过，跳过: 2025-07-20T23:25:15_123456
```
**说明**: 正常行为，避免重复发送

### 调试信息
- **日志文件**: `telegram_bot.log`
- **已发送记录**: `sent_telegram_messages.json`
- **运行状态**: 每30秒输出一次状态信息

## 🔄 重启和管理

### 安全停止
```bash
# 按 Ctrl+C 安全停止
🛑 收到停止信号，正在安全关闭...
📴 文件监控已停止
💾 数据已保存
👋 机器人已安全退出
```

### 重新启动
```bash
python telegram_news_bot.py
```
重启后会自动设置新的历史基线，只处理重启后的新消息。

## 📈 性能特点

- **内存占用**: 约50-100MB
- **CPU占用**: 低，仅在检测到文件变化时处理
- **网络要求**: 稳定的Telegram API访问
- **响应速度**: 实时检测文件变化，2秒内处理
- **错误恢复**: 自动重试机制，网络问题自动恢复

## 🔒 安全特性

- **API密钥保护**: 已配置在代码中
- **频率限制**: 内置发送间隔和重试机制
- **错误处理**: 完善的异常捕获和日志记录
- **重复检测**: 防止重复发送相同消息

## 💡 使用建议

1. **保持运行**: 建议7x24小时运行以确保实时推送
2. **网络稳定**: 确保稳定的网络连接访问Telegram API
3. **日志监控**: 定期查看日志了解运行状态
4. **配置备份**: 备份配置文件以防意外修改

## 🔗 相关文件

- **主程序**: `telegram_news_bot.py`
- **配置文件**: `telegram_config.py`  
- **输入文件**: `financial_news_ai.json`
- **日志文件**: `telegram_bot.log`
- **发送记录**: `sent_telegram_messages.json`
