#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财经新闻监控系统统一启动器
按顺序启动三个服务并统一显示日志
"""

import os
import sys
import time
import subprocess
import threading
import queue
from datetime import datetime
from pathlib import Path

# 配置
BASE_DIR = Path(r"C:\Users\<USER>\Desktop\正式版项目备份-20250718\jin10 - 副本 (4)")
SERVICES = [
    {
        "name": "网站监控器",
        "file": "jin10_web_monitor.py",
        "delay": 0,  # 立即启动
        "color": "36"  # 青色
    },
    {
        "name": "AI处理器", 
        "file": "financial_news_ai_processor.py",
        "delay": 60,  # 60秒后启动
        "color": "33"  # 黄色
    },
    {
        "name": "Telegram机器人",
        "file": "telegram_news_bot.py", 
        "delay": 5,  # AI处理器启动5秒后启动
        "color": "35"  # 紫色
    }
]

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes = {}
        self.log_queue = queue.Queue()
        self.running = True
        
    def colored_print(self, text, color_code="37"):
        """彩色打印"""
        print(f"\033[{color_code}m{text}\033[0m")
        
    def log_reader(self, process, service_name, color_code):
        """读取进程输出并添加到日志队列"""
        try:
            while self.running and process.poll() is None:
                line = process.stdout.readline()
                if line:
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    log_entry = {
                        "timestamp": timestamp,
                        "service": service_name,
                        "message": line.strip(),
                        "color": color_code
                    }
                    self.log_queue.put(log_entry)
                    
                # 同时读取错误输出
                if process.stderr:
                    error_line = process.stderr.readline()
                    if error_line:
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        log_entry = {
                            "timestamp": timestamp,
                            "service": service_name,
                            "message": f"ERROR: {error_line.strip()}",
                            "color": "31"  # 红色表示错误
                        }
                        self.log_queue.put(log_entry)
        except Exception as e:
            print(f"日志读取器错误 ({service_name}): {e}")
            
    def start_service(self, service_config):
        """启动单个服务"""
        service_name = service_config["name"]
        file_path = BASE_DIR / service_config["file"]
        
        if not file_path.exists():
            self.colored_print(f"❌ 文件不存在: {file_path}", "31")
            return None
            
        try:
            # 启动进程
            process = subprocess.Popen(
                [sys.executable, str(file_path)],
                cwd=str(BASE_DIR),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes[service_name] = process
            
            # 启动日志读取线程
            log_thread = threading.Thread(
                target=self.log_reader,
                args=(process, service_name, service_config["color"]),
                daemon=True
            )
            log_thread.start()
            
            self.colored_print(f"✅ {service_name} 启动成功 (PID: {process.pid})", "32")
            return process
            
        except Exception as e:
            self.colored_print(f"❌ {service_name} 启动失败: {e}", "31")
            return None
    
    def display_logs(self):
        """显示统一日志"""
        while self.running:
            try:
                # 非阻塞获取日志
                log_entry = self.log_queue.get(timeout=1)
                
                # 格式化输出
                formatted_log = f"[{log_entry['timestamp']}] [{log_entry['service']}] {log_entry['message']}"
                self.colored_print(formatted_log, log_entry['color'])
                
            except queue.Empty:
                continue
            except KeyboardInterrupt:
                break
                
    def stop_all_services(self):
        """停止所有服务"""
        self.running = False
        self.colored_print("\n🛑 正在停止所有服务...", "33")
        
        for service_name, process in self.processes.items():
            try:
                if process.poll() is None:  # 进程还在运行
                    process.terminate()
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        self.colored_print(f"✅ {service_name} 已停止", "32")
                    except subprocess.TimeoutExpired:
                        process.kill()
                        self.colored_print(f"⚠️ {service_name} 强制终止", "33")
            except Exception as e:
                self.colored_print(f"❌ 停止 {service_name} 时出错: {e}", "31")
    
    def run(self):
        """运行服务管理器"""
        try:
            self.colored_print("=" * 60, "36")
            self.colored_print("🚀 财经新闻监控系统启动器", "36")
            self.colored_print("=" * 60, "36")
            
            # 检查基础目录
            if not BASE_DIR.exists():
                self.colored_print(f"❌ 基础目录不存在: {BASE_DIR}", "31")
                return
                
            self.colored_print(f"📁 工作目录: {BASE_DIR}", "37")
            self.colored_print("", "37")
            
            # 按顺序启动服务
            for i, service_config in enumerate(SERVICES):
                if i == 0:
                    # 第一个服务立即启动
                    self.colored_print(f"🔄 启动 {service_config['name']}...", "33")
                    self.start_service(service_config)
                else:
                    # 后续服务需要等待
                    delay = service_config['delay']
                    self.colored_print(f"⏰ 等待 {delay} 秒后启动 {service_config['name']}...", "33")
                    
                    # 倒计时显示
                    for remaining in range(delay, 0, -1):
                        if not self.running:
                            return
                        print(f"\r⏳ 倒计时: {remaining} 秒", end="", flush=True)
                        time.sleep(1)
                    print()  # 换行
                    
                    self.colored_print(f"🔄 启动 {service_config['name']}...", "33")
                    self.start_service(service_config)
            
            self.colored_print("", "37")
            self.colored_print("=" * 60, "32")
            self.colored_print("✅ 所有服务启动完成！开始显示统一日志", "32")
            self.colored_print("💡 按 Ctrl+C 停止所有服务", "33")
            self.colored_print("=" * 60, "32")
            self.colored_print("", "37")
            
            # 开始显示日志
            self.display_logs()
            
        except KeyboardInterrupt:
            self.colored_print("\n🛑 收到停止信号...", "33")
        except Exception as e:
            self.colored_print(f"❌ 启动器运行错误: {e}", "31")
        finally:
            self.stop_all_services()
            self.colored_print("👋 启动器已退出", "37")

def main():
    """主函数"""
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        sys.exit(1)
    
    # 创建并运行服务管理器
    manager = ServiceManager()
    manager.run()

if __name__ == "__main__":
    main()