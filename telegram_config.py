"""
Telegram机器人配置文件

请根据您的需求修改以下配置
"""

# ==================== Telegram Bot配置 ====================

# 您的Bot <PERSON>ken (从 @BotFather 获取)
BOT_TOKEN = "8124246725:AAE1Uq5S1BLfwWXSF0TllGhtPNA6I_GKXss"

# 目标频道ID (格式: @channel_name 或 -1001234567890)
CHANNEL_ID = "-1002558446307"

# ==================== 标签过滤配置 ====================

# 目标标签 - 只有包含这些标签的消息才会被发送
TARGET_TAGS = [
    "重要", "加密货币相关", "特朗普", "马斯克", "以色列"
    # 政治人物
    # "特朗普", "拜登", "习近平", "普京",
    
    # 央行和机构
    # "美联储", "欧洲央行", "中国人民银行", "日本央行",
    # "IMF", "世界银行", "WTO",
    
    # 国家和地区
    # "美国", "中国", "欧洲", "日本", "英国", "德国", "法国",
    
    # 经济指标
    # "利率", "通胀", "GDP", "CPI", "PPI", "就业", "失业率",
    # "贸易", "关税", "制裁", "汇率", "进出口", "出口", "进口",
    # "贸易额", "增长", "口岸",
    
    # 金融市场
    # "股市", "债券", "期货", "外汇",
    # "纳斯达克", "道琼斯", "标普500", "上证指数",
    
    # 大宗商品
    # "黄金", "白银", "原油", "天然气", "铜", "铁矿石",
    
    # 加密货币
    # "比特币", "以太坊", "加密货币", "数字货币",
    
    # 公司和行业
    # "苹果", "微软", "谷歌", "亚马逊", "特斯拉",
    # "银行", "科技", "能源", "医疗", "房地产",
    
    # 其他重要关键词
    # "财报", "IPO", "并购", "破产", "违约",
    # "疫情", "战争", "地缘政治", "气候变化"
]

# ==================== 发送配置 ====================

# 消息发送间隔 (秒)
SEND_DELAY = 2

# Telegram消息最大长度
MAX_MESSAGE_LENGTH = 4000

# 发送失败重试次数
RETRY_ATTEMPTS = 5

# ==================== 消息格式配置 ====================

# 消息模板 - 内容 + 分隔线 + 时间
MESSAGE_TEMPLATE = """{content}

——————————
{time}"""

# 是否启用Markdown格式
USE_MARKDOWN = True

# 是否在消息前添加emoji
USE_EMOJI = True

# ==================== 高级配置 ====================

# 是否启用调试模式
DEBUG_MODE = False

# 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR')
LOG_LEVEL = 'INFO'

# 是否保存发送历史
SAVE_SENT_HISTORY = True

# 文件监控冷却时间 (秒)
FILE_WATCH_COOLDOWN = 2
