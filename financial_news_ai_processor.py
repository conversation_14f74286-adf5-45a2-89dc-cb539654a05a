#!/usr/bin/env python3
"""
Financial News AI Processor - 完整的单文件AI模块
==============================================

这个模块监控 financial_news.json 文件的新内容，并通过 Google Gemini API 进行处理。
它监视文件变化，提取新消息，发送到 Gemini 进行AI处理，并将结果保存到 financial_news_ai.json。

快速开始:
========
1. 安装依赖: pip install requests watchdog
2. 运行: python financial_news_ai_processor.py
注意: API密钥已内置，无需额外配置

使用方法:
========
# 自动监控模式 (推荐)
python financial_news_ai_processor.py

# 手动处理模式
python financial_news_ai_processor.py --manual

# 限制处理数量
python financial_news_ai_processor.py --manual --count 5

# 测试模式
python financial_news_ai_processor.py --test

# 创建测试数据
python financial_news_ai_processor.py --create-test

输出格式:
========
financial_news_ai.json 文件格式:
[
  {
    "timestamp": "2025-06-26T08:30:15.123456",
    "content": "AI生成的摘要内容 #关键词1 #关键词2 #关键词3"
  }
]

功能特性:
========
- 自动文件监控
- AI内容处理 (使用中文提示词)
- 重复检测 (避免重复处理)
- 错误处理和日志记录
- 手动和自动两种模式
- 测试功能
"""

import json
import time
import logging
import os
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# 尝试导入依赖，如果失败则提供安装提示
try:
    import requests
except ImportError:
    print("错误: 缺少 requests 库")
    print("请运行: pip install requests")
    sys.exit(1)

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError:
    print("错误: 缺少 watchdog 库")
    print("请运行: pip install watchdog")
    sys.exit(1)

# ==================== 配置部分 ====================

# 文件路径配置
FINANCIAL_NEWS_FILE = Path("financial_news.json")
AI_OUTPUT_FILE = Path("financial_news_ai.json")
PROCESSED_IDS_FILE = Path("processed_news_ids.json")
LOG_FILE = Path("financial_news_ai.log")

# API配置
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"
GEMINI_API_KEY = "AIzaSyA27reYRj0LnIZwSPOWhpO8wrSS6HVyzmo"  # 您提供的API密钥
RATE_LIMIT_DELAY = 1  # API调用间隔(秒)
FILE_WATCH_COOLDOWN = 2  # 文件监控冷却时间(秒)

# 中文提示词模板
PROMPT_TEMPLATE = """你是一名资深的信息编辑，擅长用简洁精准的语言提炼复杂信息的核心内容。请阅读下方提供的信息，将其加工为一个结构清晰、语言流畅、内容准确的摘要段落，全面呈现关键信息、核心结果或主要影响，保持客观中立，不添加任何原文未提及的主观判断或外部内容。随后，请基于原始信息提炼出关键词，直接以"#关键词1 #关键词2 #关键词3 ... #关键词n"的格式紧跟在摘要段落末尾输出，且摘要与关键词必须在同一行内、不换行呈现，用于概括核心主题或重点要素。

请严格按照本提示中的格式要求进行输出，不得更改段落结构、关键词格式或输出顺序。所有输出必须为纯文本格式，不得包含任何 Markdown 标记、换行符、额外符号或格式化元素。

领域判断: 如果新闻内容与加密货币有直接或间接关系，请在所有关键词的最后面加上 #加密货币相关 标签。

如果新闻属于以下任何一类，请在所有关键词的最前面加上 #重要 标签：
1. 宏观决策与政策：主要经济体的央行关键决策（如利率变动）、重大财政或贸易政策变动、影响深远的司法判决或监管行动。
2. 地缘政治与安全：高级别政治声明、国家间军事冲突、重大恐怖袭击、地缘政治格局的显著变化。
3. 系统性风险与危机：可能引发连锁反应的金融市场事件（如大型机构倒闭、流动性危机）、影响全球或大范围区域的自然灾害或公共卫生危机。

特定人物标签：如果新闻内容提及唐纳德·特朗普(Donald Trump)或埃隆·马斯克(Elon Musk)，关键词中必须分别包含 #特朗普 或 #马斯克。

示例：

原始信息：
:21 美国总统特朗普重申，伊朗的所有三个核设施都已被完全摧毁。
处理结果：
美国总统特朗普重申，伊朗境内的三座核设施已被彻底摧毁，进一步加剧了中东地区的紧张局势。#特朗普 #伊朗 #核设施 #中东 #重要

原始信息：
:04 美国总统特朗普：我已要求司法部公布大陪审团有关杰弗里·爱泼斯坦的所有证词，但必须获得法院批准。话虽如此，即使法院给予充分而坚定的批准，对于提出这一要求的麻烦制造者和激进左翼疯子来说，什么都是不够的，他们会要求更多。
处理结果：
特朗普表示已要求司法部公开与杰弗里·爱泼斯坦相关的大陪审团证词，但需经法院批准，并批评反对者永不满足。#特朗普 #爱泼斯坦 #司法部 #大陪审团

请你处理：{news_content}"""

# 配置日志
def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

# ==================== 主处理类 ====================

class FinancialNewsProcessor:
    """财经新闻AI处理器主类"""

    def __init__(self, api_key: Optional[str] = None):
        """
        初始化处理器

        Args:
            api_key: Google Gemini API密钥，如果为None则使用默认密钥
        """
        # 使用提供的API密钥或默认密钥
        self.api_key = api_key or GEMINI_API_KEY
        if not self.api_key:
            raise ValueError("未提供Gemini API密钥。")

        # 加载已处理的ID以避免重复处理
        self.processed_ids = self._load_processed_ids()

        # 启动时检查历史数据，同步已处理的ID
        self._sync_processed_ids_from_history()
    
    def _load_processed_ids(self) -> set:
        """加载已处理的新闻ID集合"""
        try:
            if PROCESSED_IDS_FILE.exists():
                with open(PROCESSED_IDS_FILE, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
        except Exception as e:
            logger.warning(f"无法加载已处理ID: {e}")
        return set()

    def _save_processed_ids(self):
        """保存已处理的新闻ID集合"""
        try:
            with open(PROCESSED_IDS_FILE, 'w', encoding='utf-8') as f:
                json.dump(list(self.processed_ids), f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"无法保存已处理ID: {e}")

    def _sync_processed_ids_from_history(self):
        """基于financial_news.json的最后一条消息作为历史分界线，只处理之后的新消息"""
        try:
            # 加载财经新闻
            financial_news = self._load_financial_news()
            if not financial_news:
                logger.info("未找到财经新闻文件，从零开始")
                return

            # 找到最后一条消息作为历史分界线
            if not financial_news:
                logger.info("财经新闻文件为空，从零开始")
                return

            # 按saved_at时间排序，找到最新的一条消息
            sorted_news = sorted(financial_news, key=lambda x: x.get('saved_at', ''))
            last_news = sorted_news[-1]
            last_news_id = last_news.get('id', '')
            last_news_time = last_news.get('saved_at', '')

            if not last_news_id:
                logger.warning("无法获取最后一条消息的ID，从零开始")
                return

            logger.info(f"以最后一条消息作为历史分界线: {last_news_id} (时间: {last_news_time})")

            # 将这条消息及之前的所有消息标记为已处理
            synced_count = 0
            for news_item in financial_news:
                news_id = news_item.get('id', '')
                news_time = news_item.get('saved_at', '')

                # 如果这条消息的时间小于等于分界线时间，标记为已处理
                if news_id and news_time <= last_news_time:
                    if news_id not in self.processed_ids:
                        self.processed_ids.add(news_id)
                        synced_count += 1

            if synced_count > 0:
                # 保存更新后的已处理ID列表
                self._save_processed_ids()
                logger.info(f"基于历史分界线，新增标记了 {synced_count} 个已处理的新闻ID")
                logger.info(f"当前已处理ID总数: {len(self.processed_ids)}")
            else:
                logger.info("所有历史消息已经在已处理列表中")

        except Exception as e:
            logger.error(f"同步历史ID时出错: {e}")
            logger.info("将继续使用现有的已处理ID列表")

    def _load_financial_news(self) -> List[Dict[str, Any]]:
        """从JSON文件加载财经新闻"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if not FINANCIAL_NEWS_FILE.exists():
                    logger.warning(f"财经新闻文件不存在: {FINANCIAL_NEWS_FILE}")
                    return []

                with open(FINANCIAL_NEWS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)

            except json.JSONDecodeError as e:
                if attempt < max_retries - 1:
                    logger.warning(f"JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(1)  # 等待1秒后重试
                    continue
                else:
                    logger.error(f"JSON解析最终失败: {e}")
                    return []
            except Exception as e:
                logger.error(f"加载财经新闻错误: {e}")
                return []

        return []

    def _load_ai_output(self) -> List[Dict[str, Any]]:
        """加载现有的AI输出"""
        try:
            if not AI_OUTPUT_FILE.exists():
                return []

            with open(AI_OUTPUT_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content or content == "[]":
                    return []

                # 重新打开文件进行JSON解析
                f.seek(0)
                data = json.load(f)
                return data if isinstance(data, list) else []

        except json.JSONDecodeError as e:
            logger.warning(f"AI输出文件JSON格式错误: {e}，将尝试修复")
            return self._repair_ai_output_file()
        except Exception as e:
            logger.warning(f"加载AI输出错误: {e}，将创建新文件")
            return []

    def _repair_ai_output_file(self) -> List[Dict[str, Any]]:
        """尝试修复损坏的AI输出文件"""
        try:
            with open(AI_OUTPUT_FILE, 'r', encoding='utf-8') as f:
                content = f.read()

            # 尝试找到有效的JSON对象
            lines = content.split('\n')
            valid_objects = []
            current_obj = ""
            brace_count = 0

            for line in lines:
                current_obj += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0 and current_obj.strip():
                    try:
                        # 尝试解析当前对象
                        if current_obj.strip().startswith('{'):
                            obj = json.loads(current_obj.strip())
                            valid_objects.append(obj)
                    except:
                        pass
                    current_obj = ""

            if valid_objects:
                # 保存修复后的文件
                with open(AI_OUTPUT_FILE, 'w', encoding='utf-8') as f:
                    json.dump(valid_objects, f, ensure_ascii=False, indent=2)
                logger.info(f"成功修复AI输出文件，恢复了 {len(valid_objects)} 条记录")
                return valid_objects
            else:
                logger.warning("无法修复AI输出文件，将重新开始")
                return []

        except Exception as e:
            logger.error(f"修复AI输出文件失败: {e}")
            return []

    def _save_ai_output(self, ai_responses: List[Dict[str, Any]]):
        """保存AI响应到输出文件"""
        try:
            with open(AI_OUTPUT_FILE, 'w', encoding='utf-8') as f:
                json.dump(ai_responses, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存 {len(ai_responses)} 条AI响应到 {AI_OUTPUT_FILE}")
        except Exception as e:
            logger.error(f"保存AI输出错误: {e}")

    def _save_single_ai_response(self, response_obj: Dict[str, Any]):
        """保存单条AI响应到输出文件 - 真正的追加模式"""
        try:
            # 检查文件是否存在
            if not AI_OUTPUT_FILE.exists():
                # 文件不存在，创建新的JSON数组
                with open(AI_OUTPUT_FILE, 'w', encoding='utf-8') as f:
                    json.dump([response_obj], f, ensure_ascii=False, indent=2)
                logger.info(f"创建新文件并保存第一条AI响应到 {AI_OUTPUT_FILE}")
            else:
                # 文件存在，读取现有内容并追加
                try:
                    with open(AI_OUTPUT_FILE, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content and content != "[]":
                            existing_responses = json.loads(content)
                        else:
                            existing_responses = []
                except:
                    # 如果文件损坏，重新开始
                    existing_responses = []

                # 追加新响应
                existing_responses.append(response_obj)

                # 写回文件
                with open(AI_OUTPUT_FILE, 'w', encoding='utf-8') as f:
                    json.dump(existing_responses, f, ensure_ascii=False, indent=2)

                logger.info(f"已追加单条AI响应到 {AI_OUTPUT_FILE}，当前总数: {len(existing_responses)}")

        except Exception as e:
            logger.error(f"保存单条AI输出错误: {e}")

    def _process_with_gemini(self, news_content: str) -> Optional[str]:
        """
        使用Gemini AI处理新闻内容

        Args:
            news_content: 要处理的新闻内容

        Returns:
            AI响应文本，如果处理失败则返回None
        """
        try:
            prompt = PROMPT_TEMPLATE.format(news_content=news_content)

            # 构建请求数据
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ]
            }

            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'X-goog-api-key': self.api_key
            }

            # 发送POST请求
            response = requests.post(GEMINI_API_URL, json=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text']
                    return content.strip()
                else:
                    logger.warning("Gemini返回空响应")
                    return None
            else:
                logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error("API请求超时")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求错误: {e}")
            return None
        except Exception as e:
            logger.error(f"Gemini处理错误: {e}")
            return None
    
    def get_new_messages(self) -> List[Dict[str, Any]]:
        """
        获取尚未处理的新消息

        Returns:
            新消息字典列表
        """
        all_news = self._load_financial_news()
        new_messages = []

        for news_item in all_news:
            news_id = news_item.get('id')
            if news_id and news_id not in self.processed_ids:
                new_messages.append(news_item)

        logger.info(f"发现 {len(new_messages)} 条新消息待处理")
        return new_messages

    def process_new_messages(self, max_count: Optional[int] = None) -> int:
        """
        处理所有新消息并保存结果 - 每处理一条立即保存一条

        Args:
            max_count: 最大处理数量，None表示处理所有

        Returns:
            成功处理的消息数量
        """
        new_messages = self.get_new_messages()
        if not new_messages:
            logger.info("没有新消息需要处理")
            return 0

        # 限制处理数量
        if max_count:
            new_messages = new_messages[:max_count]
            logger.info(f"限制处理前 {len(new_messages)} 条消息")

        processed_count = 0

        for news_item in new_messages:
            news_id = news_item.get('id')
            news_content = news_item.get('content', '')

            if not news_content:
                logger.warning(f"新闻ID内容为空: {news_id}")
                continue

            logger.info(f"正在处理新闻ID: {news_id}")

            # 使用Gemini处理
            ai_response = self._process_with_gemini(news_content)

            if ai_response:
                # 创建响应对象
                response_obj = {
                    "timestamp": datetime.now().isoformat(),
                    "content": ai_response
                }

                # 立即保存这一条处理结果
                self._save_single_ai_response(response_obj)

                # 标记为已处理
                self.processed_ids.add(news_id)
                self._save_processed_ids()

                processed_count += 1
                logger.info(f"成功处理并保存新闻ID: {news_id}")

                # 添加延迟避免API限制
                time.sleep(RATE_LIMIT_DELAY)
            else:
                logger.error(f"处理失败新闻ID: {news_id}")

        logger.info(f"成功处理 {processed_count} 条消息")
        return processed_count


# ==================== 文件监控类 ====================

class FileWatcher(FileSystemEventHandler):
    """文件系统事件处理器，用于监控financial_news.json的变化"""

    def __init__(self, processor: FinancialNewsProcessor):
        self.processor = processor
        self.last_modified = 0
        self.process_count = 0

    def on_modified(self, event):
        """处理文件修改事件"""
        if event.is_directory:
            return

        if event.src_path.endswith('financial_news.json'):
            # 避免重复处理同一次修改
            current_time = time.time()
            if current_time - self.last_modified < FILE_WATCH_COOLDOWN:
                return

            self.last_modified = current_time
            self.process_count += 1

            logger.info("🔔 检测到财经新闻文件更新！")
            logger.info(f"📥 开始处理新消息... (第 {self.process_count} 次触发)")

            try:
                processed = self.processor.process_new_messages()
                if processed > 0:
                    logger.info(f"✅ 成功处理 {processed} 条新消息")
                    logger.info(f"💾 结果已保存到 {AI_OUTPUT_FILE}")
                else:
                    logger.info("ℹ️  未发现新消息需要处理")

                logger.info("🔄 继续监控文件变化...")

            except Exception as e:
                logger.error(f"❌ 处理新消息时发生错误: {e}")
                logger.info("🔄 监控继续运行...")


# ==================== 工具函数 ====================

def create_test_data():
    """创建测试数据"""
    test_data = [
        {
            "id": "test_001",
            "timestamp": "2025-06-26 08:00:00",
            "source": "测试数据",
            "content": ":00 美联储宣布维持利率不变，符合市场预期。",
            "saved_at": datetime.now().isoformat()
        }
    ]

    with open(FINANCIAL_NEWS_FILE, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)

    print(f"已创建测试数据: {FINANCIAL_NEWS_FILE}")

def test_processor():
    """测试处理器功能"""
    print("测试财经新闻AI处理器...")

    try:
        processor = FinancialNewsProcessor()
        print("✓ 处理器初始化成功")

        # 检查文件
        if not FINANCIAL_NEWS_FILE.exists():
            print("⚠ 财经新闻文件不存在，创建测试数据...")
            create_test_data()

        # 处理消息
        count = processor.process_new_messages(max_count=1)
        print(f"✓ 处理了 {count} 条消息")

        return True

    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


# ==================== 主函数 ====================

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='财经新闻AI处理器')
    parser.add_argument('--manual', action='store_true', help='手动处理模式')
    parser.add_argument('--test', action='store_true', help='测试模式')
    parser.add_argument('--count', type=int, help='处理消息数量限制')
    parser.add_argument('--create-test', action='store_true', help='创建测试数据')

    args = parser.parse_args()

    try:
        if args.create_test:
            create_test_data()
            return

        if args.test:
            success = test_processor()
            sys.exit(0 if success else 1)

        # 初始化处理器
        processor = FinancialNewsProcessor()

        if args.manual:
            # 手动处理模式
            logger.info("手动处理模式...")
            count = processor.process_new_messages(max_count=args.count)
            logger.info(f"手动处理完成，共处理 {count} 条消息")
        else:
            # 自动监控模式 - 常驻运行
            logger.info("=== 启动AI模块常驻监控模式 ===")
            logger.info("处理现有未处理消息...")
            processor.process_new_messages()

            # 设置文件监控
            event_handler = FileWatcher(processor)
            observer = Observer()
            observer.schedule(event_handler, path='.', recursive=False)

            # 开始监控
            observer.start()
            logger.info("🚀 AI模块已启动，正在常驻监控 financial_news.json 文件变化...")
            logger.info("📊 监控状态: 活跃")
            logger.info("⏰ 检查间隔: 实时监控")
            logger.info("🔄 处理模式: 自动处理新消息")
            logger.info("📝 日志文件: financial_news_ai.log")
            logger.info("🛑 停止方式: 按 Ctrl+C")
            logger.info("=" * 50)

            try:
                # 常驻运行，每30秒输出一次状态
                counter = 0
                while True:
                    time.sleep(30)
                    counter += 1
                    if counter % 2 == 0:  # 每60秒输出一次详细状态
                        processed_count = len(processor.processed_ids)
                        logger.info(f"📊 运行状态: 正常 | 已处理消息: {processed_count} 条 | 运行时间: {counter * 30} 秒")

            except KeyboardInterrupt:
                logger.info("🛑 收到停止信号，正在安全关闭...")
                observer.stop()
                logger.info("📴 文件监控已停止")
                logger.info("💾 数据已保存")
                logger.info("👋 AI模块已安全退出")

            observer.join()

    except Exception as e:
        logger.error(f"主函数错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
